import type { SVGProps } from "react";
const SvgVip = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 23 11"
        {...props}
    >
        <rect width={23} height={9} y={2} fill="url(#vip_svg__a)" rx={4.5} />
        <g filter="url(#vip_svg__b)">
            <path
                fill="url(#vip_svg__c)"
                d="M14.82 4.43h2.315a4 4 0 0 0-.369-.616l.495-.181q.225.337.407.797h2.04v.506h-2.86l-.043.864h2.266q-.024 1.773-.154 2.227-.18.66-.83.677c-.177 0-.391-.011-.633-.028l-.154-.473c.291.011.539.022.737.022.23-.01.38-.176.445-.506.033-.187.05-.665.06-1.424h-1.798a3.7 3.7 0 0 1-.407 1.166q-.406.71-1.133 1.303l-.38-.363c.584-.49.969-.94 1.156-1.358q.279-.627.319-1.403.016-.455.033-.704h-1.513z"
            />
            <path
                fill="url(#vip_svg__d)"
                d="M14.098 4.122v1.205h-.528v-.699H9.93v.693H9.4V4.122h2.063a3.4 3.4 0 0 0-.259-.434l.567-.088c.077.154.154.324.23.522zm-.78.94V6.52h-2.64v.484h2.892v1.733h-.517v-.198h-2.376v.203h-.517v-3.68zm-2.64 3.026h2.375V7.45h-2.376zm0-2.019h2.128v-.555h-2.129z"
            />
        </g>
        <g filter="url(#vip_svg__e)">
            <path
                fill="url(#vip_svg__f)"
                d="M3.33.352h.1l.596 3.66q.23 1.473.29 1.965c.038-.097.212-.424.54-1.021.327-.597.519-.963.596-1.08l1.345-2.29h-.582V.333h3.764l-5.163 8.4H2.871L1.442 1.586H.82V.332h2.51z"
            />
        </g>
        <defs>
            <linearGradient
                id="vip_svg__a"
                x1={15.923}
                x2={15.989}
                y1={6.5}
                y2={11.454}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#0083FF" />
                <stop offset={0.469} stopColor="#3BA0FF" />
                <stop offset={1} stopColor="#7DC0FF" />
            </linearGradient>
            <linearGradient
                id="vip_svg__c"
                x1={17.963}
                x2={16.831}
                y1={6.853}
                y2={10.016}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#AED7FD" />
            </linearGradient>
            <linearGradient
                id="vip_svg__d"
                x1={12.421}
                x2={11.25}
                y1={6.827}
                y2={9.965}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#AED7FD" />
            </linearGradient>
            <linearGradient
                id="vip_svg__f"
                x1={6.708}
                x2={4.873}
                y1={5.603}
                y2={9.429}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#AED7FD" />
            </linearGradient>
            <filter
                id="vip_svg__b"
                width={10.681}
                height={5.538}
                x={9.401}
                y={3.6}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={0.374} dy={0.374} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.0414705 0 0 0 0 0.27647 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_2677_18912"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_2677_18912"
                    result="shape"
                />
            </filter>
            <filter
                id="vip_svg__e"
                width={9.788}
                height={9.026}
                x={0.819}
                y={0.332}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={0.627} dy={0.627} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.0414705 0 0 0 0 0.27647 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_2677_18912"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_2677_18912"
                    result="shape"
                />
            </filter>
        </defs>
    </svg>
);
export default SvgVip;
